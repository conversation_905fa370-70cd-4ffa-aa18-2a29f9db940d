# 水幕课程App API端点架构设计文档

## 📋 文档信息
- **创建时间**: 2025-06-29
- **版本**: v1.0
- **作者**: Augment Agent
- **项目**: 水幕视频课程App

## 🎯 设计原则

### 核心理念
**按数据归属分类，而非按操作者身份设计端点**

### 设计目标
1. **逻辑清晰** - 端点分类明确，易于理解和维护
2. **权限统一** - 同一数据的权限控制逻辑统一
3. **减少重复** - 避免功能重复，降低维护成本
4. **扩展性好** - 新增数据类型时容易决定归属
5. **安全性高** - 统一的权限控制，减少安全漏洞

## 📊 端点分类架构

### 1. 用户个人数据端点 (`/api/users/`)
**用途**: 用户个人相关数据的CRUD操作  
**使用者**: App客户端 + 管理端  
**权限控制**: 
- App用户：只能操作自己的数据（通过token验证user_id）
- 管理员：可以操作任何用户的数据（通过admin权限验证）

#### 端点列表
```
PUT /api/users/{user_id}/progress/{video_id}    - 更新观看进度
PUT /api/users/{user_id}/settings               - 更新个人设置  
PUT /api/users/{user_id}/cache/{video_id}       - 更新缓存状态
PUT /api/users/{user_id}/favorites              - 更新收藏状态
PUT /api/users/{user_id}/stats                  - 更新学习统计
GET /api/users/{user_id}/profile                - 获取用户资料
GET /api/users/{user_id}/purchases              - 获取购买记录
```

### 2. 内容数据端点 (`/api/`)
**用途**: 课程内容数据的基础CRUD操作  
**使用者**: App客户端（读取） + 管理端（读写）  
**权限控制**:
- App用户：只读权限
- 管理员：读写权限

#### 端点列表
```
GET /api/series                                 - 获取系列列表（含用户个人化数据）
GET /api/series/{id}                            - 获取系列详情
PUT /api/series/{id}                            - 更新系列信息

GET /api/categories                             - 获取分类列表
GET /api/categories/{id}                        - 获取分类详情
PUT /api/categories/{id}                        - 更新分类信息

GET /api/videos                                 - 获取视频列表
GET /api/videos/{id}                            - 获取视频详情
PUT /api/videos/{id}                            - 更新视频信息
```

### 3. 管理端专用功能端点 (`/api/admin/`)
**用途**: 管理端专用功能（分页、搜索、统计、批量操作等）  
**使用者**: 仅管理端  
**权限控制**: 仅管理员访问

#### 端点列表
```
GET /api/admin/series                           - 带分页搜索的系列管理
POST /api/admin/series                          - 创建系列
PUT /api/admin/series/{id}                      - 管理端更新系列
DELETE /api/admin/series/{id}                   - 删除系列

GET /api/admin/categories                       - 带分页搜索的分类管理
POST /api/admin/categories                      - 创建分类
PUT /api/admin/categories/{id}                  - 管理端更新分类
DELETE /api/admin/categories/{id}               - 删除分类

GET /api/admin/videos                           - 带分页搜索的视频管理
POST /api/admin/videos                          - 创建视频
PUT /api/admin/videos/{id}                      - 管理端更新视频
DELETE /api/admin/videos/{id}                   - 删除视频

GET /api/admin/users                            - 用户管理列表
PUT /api/admin/users/{id}                       - 更新用户基本信息
```

## 🔧 权限控制策略

### 权限级别
1. **普通用户** - App客户端用户
2. **管理员** - 管理端用户

### 权限矩阵

| 端点类型 | 普通用户 | 管理员 | 说明 |
|---------|---------|--------|------|
| 用户个人数据端点 | 仅自己的数据 | 所有用户数据 | 通过user_id验证 |
| 内容数据端点 | 只读 | 读写 | 通过admin权限验证 |
| 管理端专用端点 | 禁止访问 | 完全访问 | 仅admin权限 |

### 权限验证实现
```python
# 用户个人数据端点权限验证
def verify_user_data_permission(current_user, target_user_id):
    if current_user.is_admin:
        return True  # 管理员可以操作任何用户数据
    return current_user.id == target_user_id  # 普通用户只能操作自己的数据

# 内容数据端点权限验证
def verify_content_permission(current_user, operation):
    if operation == "READ":
        return True  # 所有用户都可以读取内容
    return current_user.is_admin  # 只有管理员可以修改内容

# 管理端专用端点权限验证
def verify_admin_permission(current_user):
    return current_user.is_admin  # 只有管理员可以访问
```

## 🚨 当前问题分析

### 问题1: 端点设计混乱
**现状**: 同一数据有多个更新端点
```
用户观看进度：
❌ PUT /api/videos/{id}/progress (App使用)
❌ PUT /api/admin/users/{user_id}/progress/{video_id} (管理端使用)

✅ 应该统一为: PUT /api/users/{user_id}/progress/{video_id}
```

### 问题2: 权限控制不清晰
**现状**: App端和管理端使用不同端点更新相同数据  
**问题**: 增加了权限验证的复杂性，容易出现数据不一致

### 问题3: 功能重复和维护成本
**现状**: 同一个业务逻辑需要在多个端点中实现  
**问题**: 数据验证逻辑重复，增加了测试和维护成本

## 🎯 迁移方案

### 第一阶段: 创建统一用户数据端点
1. **新增端点**:
   ```python
   PUT /api/users/{user_id}/progress/{video_id}
   PUT /api/users/{user_id}/settings
   PUT /api/users/{user_id}/cache/{video_id}
   ```

2. **实现权限控制逻辑**:
   - App用户只能操作自己的数据
   - 管理员可以操作任何用户的数据

### 第二阶段: 迁移现有客户端
1. **保持向后兼容**: 暂时保留现有端点
2. **逐步迁移**: App端和管理端都改用新端点
3. **测试验证**: 确保功能正常

### 第三阶段: 清理和优化
1. **废弃旧端点**: 完成迁移后移除重复端点
2. **统一数据验证逻辑**
3. **完善权限控制**
4. **优化性能和缓存**

## 📝 实施检查清单

### 服务端修改
- [ ] 创建统一的用户数据端点
- [ ] 实现权限控制逻辑
- [ ] 添加数据验证
- [ ] 更新API文档

### 管理端修改
- [ ] 修改API客户端使用新端点
- [ ] 更新权限验证逻辑
- [ ] 测试所有功能

### App端修改
- [ ] 修改API调用使用新端点
- [ ] 更新权限处理
- [ ] 测试用户数据更新功能

### 测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 权限测试
- [ ] 性能测试

## 🔄 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-29 | 初始版本，定义端点架构设计 | Augment Agent |

---

**注意**: 本文档将随着项目发展持续更新，请确保使用最新版本。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

print("🗑️ 删除并重新创建数据库...")

try:
    # 连接MySQL服务器（不指定数据库）
    connection = pymysql.connect(
        host='localhost',
        port=3306,
        user='mike',
        password='dyj217',
        charset='utf8mb4'
    )
    
    print("✅ MySQL服务器连接成功")
    
    cursor = connection.cursor()
    
    # 删除数据库
    print("🗑️ 删除数据库 shuimu_course...")
    cursor.execute("DROP DATABASE IF EXISTS shuimu_course")
    print("✅ 数据库已删除")
    
    # 重新创建数据库
    print("🔨 重新创建数据库 shuimu_course...")
    cursor.execute("CREATE DATABASE shuimu_course CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
    print("✅ 数据库已重新创建")
    
    cursor.close()
    connection.close()
    
    print("\n🎉 数据库删除并重新创建完成！")
    print("💡 现在数据库是全新的，没有任何表和数据")
    
except Exception as e:
    print(f"❌ 操作失败: {e}")

print("\n📝 注意事项:")
print("- 数据库已完全重新创建")
print("- 所有表结构和数据都已清空")
print("- 下次启动管理端时会自动创建表结构")
print("- 不会有任何模拟数据")

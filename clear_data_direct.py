#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接清空数据脚本
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

try:
    from database.models import DatabaseManager
    from utils.config import Config
    from sqlalchemy import text
    
    print("🗑️ 开始清空MySQL数据...")
    
    config = Config()
    db_config = config.get_database_config()
    print(f"📊 连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
    
    db_manager = DatabaseManager(db_config)
    session = db_manager.get_session()
    
    if session:
        print("✅ 数据库连接成功")
        
        # 清空各个表的数据（注意顺序）
        tables = ['videos', 'categories', 'series', 'users']
        
        for table in tables:
            try:
                # 先查看数据量
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"📊 清空前 {table} 表: {count} 条记录")
                
                # 清空数据
                session.execute(text(f"DELETE FROM {table}"))
                print(f"✅ 清空表 {table}")
                
            except Exception as e:
                print(f"❌ 清空表 {table} 失败: {e}")
        
        # 提交事务
        session.commit()
        print("💾 事务已提交")
        
        # 验证清空结果
        print("\n🔍 验证清空结果:")
        for table in tables:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"📊 清空后 {table} 表: {count} 条记录")
            except Exception as e:
                print(f"❌ 检查表 {table} 失败: {e}")
        
        session.close()
        print("\n🎉 MySQL数据库清空完成！")
        
    else:
        print("❌ 无法连接数据库")
        
except Exception as e:
    print(f"❌ 清空数据异常: {e}")

# 清空缓存文件
print("\n🗑️ 清空缓存文件...")
cache_files = [
    "shuimu-admin/data/cache.json",
    "shuimu-admin/data/test_cache.json"
]

for cache_file in cache_files:
    if os.path.exists(cache_file):
        try:
            os.remove(cache_file)
            print(f"✅ 删除缓存文件: {cache_file}")
        except Exception as e:
            print(f"❌ 删除缓存文件失败 {cache_file}: {e}")
    else:
        print(f"📄 缓存文件不存在: {cache_file}")

print("\n🎉 本地数据清空完成！")

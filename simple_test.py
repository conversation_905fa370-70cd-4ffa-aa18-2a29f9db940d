#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

print("🚀 开始简单测试...")

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

try:
    print("📦 导入模块...")
    from database.models import DatabaseManager, Series
    print("✅ 数据库模块导入成功")
    
    from cache.global_data_manager import global_data_manager
    print("✅ 全局数据管理器导入成功")
    
    # 测试数据库连接
    print("\n🔍 测试数据库连接...")
    db_manager = DatabaseManager()
    session = db_manager.get_session()
    
    # 检查系列数量
    count = session.query(Series).count()
    print(f"📊 数据库中系列数量: {count}")
    
    # 如果有数据，显示前几个
    if count > 0:
        series_list = session.query(Series).limit(3).all()
        print("📊 前3个系列:")
        for series in series_list:
            print(f"   ID: {repr(series.id)} ({type(series.id)}), 标题: {series.title}")
    else:
        print("⚠️ 数据库中没有系列数据")
    
    session.close()
    
    # 测试服务端连接
    print("\n🔍 测试服务端连接...")
    import requests
    
    response = requests.get("http://localhost:8000/api/series", timeout=5)
    if response.status_code == 200:
        data = response.json()
        print(f"📊 服务端系列数量: {len(data) if isinstance(data, list) else 'N/A'}")
        
        if isinstance(data, list) and len(data) > 0:
            first_series = data[0]
            series_id = first_series.get('id')
            series_title = first_series.get('title')
            print(f"📊 第一个系列: ID={repr(series_id)} ({type(series_id)}), 标题={series_title}")
    else:
        print(f"❌ 服务端连接失败: {response.status_code}")
    
    print("\n✅ 简单测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

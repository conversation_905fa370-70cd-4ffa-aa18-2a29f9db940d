#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API路径
"""

import requests
import json

def test_api_paths():
    """测试各种API路径"""
    base_url = "http://localhost:8000"
    
    # 测试路径列表
    test_paths = [
        "/",
        "/api",
        "/api/admin",
        "/api/admin/series",
        "/api/admin/categories", 
        "/api/admin/videos",
        "/api/series",
        "/api/categories",
        "/api/videos"
    ]
    
    print("🧪 测试API路径...")
    print("=" * 50)
    
    for path in test_paths:
        try:
            url = base_url + path
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {path} -> 200 OK")
                try:
                    data = response.json()
                    if isinstance(data, dict) and len(data) > 0:
                        print(f"   数据: {list(data.keys())}")
                except:
                    print(f"   响应: {response.text[:100]}")
            elif response.status_code == 404:
                print(f"❌ {path} -> 404 Not Found")
            else:
                print(f"⚠️ {path} -> {response.status_code}")
                
        except Exception as e:
            print(f"💥 {path} -> 异常: {e}")
    
    print("=" * 50)

if __name__ == "__main__":
    test_api_paths()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题诊断脚本
分析数据库、服务端、内存数据的状态
"""

import sys
import os
import requests

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

from database.models import DatabaseManager, Series
from cache.global_data_manager import global_data_manager
from sqlalchemy import text

def diagnose_database():
    """诊断数据库状态"""
    print("🔍 诊断数据库状态...")
    print("=" * 50)
    
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # 1. 检查表结构
        print("📊 检查 series 表结构:")
        try:
            result = session.execute(text("DESCRIBE series"))
            for row in result:
                print(f"   {row}")
        except Exception as e:
            print(f"   ❌ 无法获取表结构: {e}")
        
        # 2. 检查数据数量
        print("\n📊 检查数据数量:")
        try:
            count = session.query(Series).count()
            print(f"   系列数量: {count}")
        except Exception as e:
            print(f"   ❌ 无法查询数据: {e}")
        
        # 3. 检查现有数据
        print("\n📊 检查现有数据:")
        try:
            series_list = session.query(Series).limit(5).all()
            for series in series_list:
                print(f"   ID: {repr(series.id)} ({type(series.id)}), 标题: {series.title}")
        except Exception as e:
            print(f"   ❌ 无法查询数据: {e}")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 数据库诊断失败: {e}")

def diagnose_server():
    """诊断服务端数据"""
    print("\n🔍 诊断服务端数据...")
    print("=" * 50)
    
    try:
        # 检查服务端API
        response = requests.get("http://localhost:8000/api/series", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 服务端响应状态: {response.status_code}")
            print(f"📊 数据类型: {type(data)}")
            print(f"📊 数据数量: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print("\n📊 前3个系列数据:")
                for i, series in enumerate(data[:3]):
                    series_id = series.get('id')
                    series_title = series.get('title')
                    print(f"   {i+1}. ID: {repr(series_id)} ({type(series_id)}), 标题: {series_title}")
                    
                    # 检查分类数据
                    categories = series.get('categories', [])
                    print(f"      分类数量: {len(categories)}")
                    
                    # 检查视频数据
                    total_videos = 0
                    for category in categories:
                        videos = category.get('videos', [])
                        total_videos += len(videos)
                    print(f"      视频数量: {total_videos}")
            
        else:
            print(f"❌ 服务端响应错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 服务端诊断失败: {e}")

def diagnose_memory():
    """诊断内存数据"""
    print("\n🔍 诊断内存数据...")
    print("=" * 50)
    
    try:
        # 检查是否已加载
        is_loaded = global_data_manager.is_data_loaded()
        print(f"📊 内存数据加载状态: {is_loaded}")
        
        if not is_loaded:
            print("⚠️ 内存数据未加载，尝试加载...")
            success = global_data_manager.load_all_data_once()
            print(f"📊 加载结果: {success}")
        
        # 获取数据统计
        summary = global_data_manager.get_data_summary()
        print(f"📊 内存数据统计: {summary}")
        
        # 检查系列数据
        if summary['series_count'] > 0:
            series_result = global_data_manager.get_series_list(page=1, page_size=3)
            if series_result.get('success'):
                print("\n📊 前3个系列数据:")
                for i, series in enumerate(series_result['data']):
                    series_id = series.get('id')
                    series_title = series.get('title')
                    print(f"   {i+1}. ID: {repr(series_id)} ({type(series_id)}), 标题: {series_title}")
        
    except Exception as e:
        print(f"❌ 内存数据诊断失败: {e}")

def diagnose_data_flow():
    """诊断数据流向"""
    print("\n🔍 诊断数据流向...")
    print("=" * 50)
    
    try:
        # 检查数据同步机制
        print("📊 数据流向分析:")
        print("   服务端 → 内存缓存 → 数据库")
        print("   ↓")
        print("   界面显示")
        
        # 检查各层数据一致性
        print("\n📊 数据一致性检查:")
        
        # 1. 服务端数据
        try:
            response = requests.get("http://localhost:8000/api/series", timeout=5)
            server_data = response.json() if response.status_code == 200 else []
            server_count = len(server_data) if isinstance(server_data, list) else 0
            print(f"   服务端系列数量: {server_count}")
        except:
            server_count = 0
            print("   服务端系列数量: 无法获取")
        
        # 2. 内存数据
        try:
            if not global_data_manager.is_data_loaded():
                global_data_manager.load_all_data_once()
            summary = global_data_manager.get_data_summary()
            memory_count = summary['series_count']
            print(f"   内存系列数量: {memory_count}")
        except:
            memory_count = 0
            print("   内存系列数量: 无法获取")
        
        # 3. 数据库数据
        try:
            db_manager = DatabaseManager()
            session = db_manager.get_session()
            db_count = session.query(Series).count()
            session.close()
            print(f"   数据库系列数量: {db_count}")
        except:
            db_count = 0
            print("   数据库系列数量: 无法获取")
        
        # 一致性分析
        print(f"\n📊 一致性分析:")
        if server_count == memory_count == db_count:
            print("   ✅ 三层数据完全一致")
        else:
            print("   ❌ 三层数据不一致")
            print(f"      服务端: {server_count}, 内存: {memory_count}, 数据库: {db_count}")
            
            if server_count > 0 and memory_count == 0:
                print("      问题: 服务端有数据，但内存加载失败")
            elif memory_count > 0 and db_count == 0:
                print("      问题: 内存有数据，但数据库为空")
            elif server_count != memory_count:
                print("      问题: 服务端和内存数据不一致")
        
    except Exception as e:
        print(f"❌ 数据流向诊断失败: {e}")

def main():
    """主诊断函数"""
    print("🚀 开始问题诊断...")
    print("🎯 目标：分析数据库、服务端、内存数据的状态")
    print("=" * 80)
    
    # 1. 诊断数据库
    diagnose_database()
    
    # 2. 诊断服务端
    diagnose_server()
    
    # 3. 诊断内存
    diagnose_memory()
    
    # 4. 诊断数据流向
    diagnose_data_flow()
    
    print("\n" + "=" * 80)
    print("🎉 诊断完成！")
    print("\n💡 根据诊断结果，可以确定:")
    print("   1. 数据库表结构和数据状态")
    print("   2. 服务端数据格式和内容")
    print("   3. 内存数据加载情况")
    print("   4. 三层数据的一致性问题")
    print("\n🔧 下一步：根据诊断结果制定修复方案")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一API测试脚本
验证统一使用 /api/series 获取所有数据
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

from api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient

def test_unified_api():
    """测试统一API"""
    print("🧪 测试统一使用 /api/series 获取所有数据...")
    print("=" * 60)
    
    try:
        # 创建API客户端
        api_client = APIClient("http://localhost:8000")
        
        # 测试系列API
        print("📚 测试系列API...")
        series_client = SeriesAPIClient(api_client)
        series_result = series_client.get_series(page=1, page_size=100)
        
        series_count = len(series_result.get('data', []))
        print(f"系列数量: {series_count}")
        
        # 测试分类API（现在从 /api/series 提取）
        print("\n📂 测试分类API（从 /api/series 提取）...")
        category_client = CategoryAPIClient(api_client)
        category_result = category_client.get_categories(page=1, page_size=100)
        
        category_count = len(category_result.get('data', []))
        print(f"分类数量: {category_count}")
        
        if category_result.get('data'):
            first_category = category_result['data'][0]
            print(f"第一个分类: {first_category.get('title')}")
            print(f"所属系列: {first_category.get('series_title')}")
            print(f"视频数量: {first_category.get('video_count', 0)}")
        
        # 测试视频API（现在从 /api/series 提取）
        print("\n📹 测试视频API（从 /api/series 提取）...")
        video_client = VideoAPIClient(api_client)
        video_result = video_client.get_videos(page=1, page_size=100)
        
        video_count = len(video_result.get('data', []))
        print(f"视频数量: {video_count}")
        
        if video_result.get('data'):
            first_video = video_result['data'][0]
            print(f"第一个视频: {first_video.get('title')}")
            print(f"所属系列: {first_video.get('series_title')}")
            print(f"所属分类: {first_video.get('category_title')}")
            print(f"时长: {first_video.get('duration', 0)}秒")
        
        # 验证数据完整性
        print(f"\n📊 数据统计:")
        print(f"  系列: {series_count} 个 (期望: 4)")
        print(f"  分类: {category_count} 个 (期望: 11)")
        print(f"  视频: {video_count} 个 (期望: 20)")
        
        # 检查是否达到预期
        success = (
            series_count >= 3 and     # 至少3个系列
            category_count >= 10 and  # 至少10个分类
            video_count >= 15         # 至少15个视频
        )
        
        if success:
            print("\n🎉 统一API测试通过！")
            print("✅ 所有数据都从 /api/series 获取")
            print("✅ 数据提取和关联正确")
            print("✅ 数据完整性良好")
            return True
        else:
            print("\n❌ 数据仍然不完整")
            print("⚠️ 需要进一步检查数据提取逻辑")
            return False
            
    except Exception as e:
        print(f"❌ 统一API测试异常: {e}")
        return False

def test_direct_series_api():
    """直接测试 /api/series API"""
    print("\n🧪 直接测试 /api/series API...")
    
    try:
        import requests
        
        response = requests.get("http://localhost:8000/api/series", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"📚 直接调用 /api/series:")
            print(f"  状态码: {response.status_code}")
            print(f"  数据类型: {type(data)}")
            print(f"  系列数量: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                # 统计分类和视频数量
                total_categories = 0
                total_videos = 0
                
                for series in data:
                    categories = series.get('categories', [])
                    total_categories += len(categories)
                    
                    for category in categories:
                        videos = category.get('videos', [])
                        total_videos += len(videos)
                
                print(f"  总分类数量: {total_categories}")
                print(f"  总视频数量: {total_videos}")
                
                return {
                    'series': len(data),
                    'categories': total_categories,
                    'videos': total_videos
                }
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 直接API调用异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🚀 开始统一API测试...")
    
    # 直接测试 /api/series
    direct_result = test_direct_series_api()
    
    # 测试API客户端
    api_ok = test_unified_api()
    
    print("=" * 60)
    print("🎯 测试结果总结:")
    
    if direct_result:
        print(f"  直接API调用: 系列={direct_result['series']}, 分类={direct_result['categories']}, 视频={direct_result['videos']}")
    
    print(f"  API客户端: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if api_ok and direct_result:
        print("\n🎉 统一API修复成功！")
        print("\n✅ 修复效果:")
        print("  1. 统一使用 /api/series 获取所有数据 ✅")
        print("  2. 正确提取系列、分类、视频数据 ✅")
        print("  3. 数据关联信息完整 ✅")
        print("  4. 避免了多个API端点的问题 ✅")
        
        print("\n🚀 现在管理端应该能够:")
        print(f"  - 显示所有{direct_result['categories']}个分类")
        print(f"  - 显示所有{direct_result['videos']}个视频")
        print(f"  - 显示{direct_result['series']}个系列")
        print("  - 数据关联信息完整")
    else:
        print("\n⚠️ 修复效果需要进一步验证")

if __name__ == "__main__":
    main()

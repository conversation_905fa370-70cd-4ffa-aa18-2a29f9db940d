#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存优化测试脚本
测试新的缓存机制是否正常工作
"""

import sys
import os
import time
import logging

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from cache.cache_manager import CacheManager
from cache.data_manager import DataManager
from database.models import DatabaseManager
from services.course_service import CourseService
from services.user_service import UserService
from utils.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_cache_manager():
    """测试缓存管理器"""
    print("🧪 测试缓存管理器...")
    
    cache_manager = CacheManager("./data/test_cache.json")
    
    # 测试保存和加载
    test_data = {
        'series': [{'id': 1, 'title': '测试系列'}],
        'categories': [{'id': 1, 'title': '测试分类'}],
        'videos': [{'id': 1, 'title': '测试视频'}],
        'users': [{'id': 1, 'username': '测试用户'}]
    }
    
    # 保存数据
    if cache_manager.save_cache(test_data):
        print("✅ 缓存保存成功")
    else:
        print("❌ 缓存保存失败")
        return False
    
    # 加载数据
    if cache_manager.load_cache():
        print("✅ 缓存加载成功")
        print(f"📊 缓存数据: {cache_manager.get_cache_info()}")
    else:
        print("❌ 缓存加载失败")
        return False
    
    return True

def test_data_manager():
    """测试数据管理器"""
    print("🧪 测试数据管理器...")
    
    data_manager = DataManager("./data/test_cache.json")
    
    # 测试数据获取
    result = data_manager.get_data('series', page=1, page_size=10)
    if result.get('success'):
        print(f"✅ 数据获取成功: {len(result.get('data', []))} 条记录")
    else:
        print(f"❌ 数据获取失败: {result.get('message')}")
        return False
    
    return True

def test_course_service_with_cache():
    """测试带缓存的课程服务"""
    print("🧪 测试带缓存的课程服务...")
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_config = config.get_database_config()
        db_manager = DatabaseManager(db_config)
        
        # 创建课程服务（启用缓存）
        course_service = CourseService(db_manager, use_api=False, use_cache=True)
        
        # 测试数据初始化
        start_time = time.time()
        if course_service.initialize_data():
            init_time = time.time() - start_time
            print(f"✅ 课程服务初始化成功，耗时: {init_time:.2f}秒")
        else:
            print("❌ 课程服务初始化失败")
            return False
        
        # 测试数据获取速度
        start_time = time.time()
        result = course_service.get_video_list(page=1, page_size=20)
        query_time = time.time() - start_time
        
        if result.get('success'):
            print(f"✅ 视频数据获取成功，耗时: {query_time:.3f}秒，数据量: {len(result.get('data', []))} 条")
        else:
            print(f"❌ 视频数据获取失败: {result.get('message')}")
            return False
        
        # 再次获取，测试缓存效果
        start_time = time.time()
        result2 = course_service.get_video_list(page=1, page_size=20)
        query_time2 = time.time() - start_time
        
        if result2.get('success'):
            print(f"✅ 第二次视频数据获取成功，耗时: {query_time2:.3f}秒（应该更快）")
            if query_time2 < query_time:
                print("🚀 缓存优化生效！第二次查询更快")
            else:
                print("⚠️ 缓存优化可能未生效")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试课程服务失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始缓存优化测试...")
    print("=" * 50)
    
    # 测试缓存管理器
    if not test_cache_manager():
        print("❌ 缓存管理器测试失败")
        return
    
    print("-" * 30)
    
    # 测试数据管理器
    if not test_data_manager():
        print("❌ 数据管理器测试失败")
        return
    
    print("-" * 30)
    
    # 测试课程服务
    if not test_course_service_with_cache():
        print("❌ 课程服务测试失败")
        return
    
    print("=" * 50)
    print("🎉 所有测试通过！缓存优化工作正常")

if __name__ == "__main__":
    main()

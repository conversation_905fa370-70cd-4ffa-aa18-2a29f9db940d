<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c2676fcb-956f-4991-b84f-5d91da13f897" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/check_db_status.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/migrate_db.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_migration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/update_db_structure.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/migrate_database.py" beforeDir="false" afterPath="$PROJECT_DIR$/migrate_database.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/client.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/client.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/database/dao.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/database/dao.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/database/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/database/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/services/course_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/services/course_service.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zA4mg24FcHxMZF4ro1zuiEeuZl" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Python.migrate_database.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/01-shuimu_01/shuimu-admin",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="migrate_database" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="shuimu-admin" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/migrate_database.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.migrate_database" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-53e2683a6804-9cdd278e9d02-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c2676fcb-956f-4991-b84f-5d91da13f897" name="更改" comment="" />
      <created>1751160703897</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751160703897</updated>
    </task>
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
  </component>
</project>
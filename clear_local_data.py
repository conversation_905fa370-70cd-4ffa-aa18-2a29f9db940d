#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空本地数据脚本
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

def clear_mysql_data():
    """清空MySQL数据库中的数据"""
    print("🗑️ 清空MySQL数据库中的数据...")
    
    try:
        from database.models import DatabaseManager
        from utils.config import Config
        
        config = Config()
        db_config = config.get_database_config()
        print(f"📊 连接数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        db_manager = DatabaseManager(db_config)
        session = db_manager.get_session()
        
        if not session:
            print("❌ 无法连接数据库")
            return False
        
        # 清空各个表的数据（注意顺序，先删除有外键的表）
        tables = ['videos', 'categories', 'series', 'users']
        
        for table in tables:
            try:
                result = session.execute(f"SELECT COUNT(*) FROM {table}")
                count = result.fetchone()[0]
                print(f"📊 清空前 {table} 表: {count} 条记录")
                
                session.execute(f"DELETE FROM {table}")
                print(f"✅ 清空表 {table}")
                
            except Exception as e:
                print(f"❌ 清空表 {table} 失败: {e}")
        
        session.commit()
        
        # 验证清空结果
        print("\n🔍 验证清空结果:")
        for table in tables:
            try:
                result = session.execute(f"SELECT COUNT(*) FROM {table}")
                count = result.fetchone()[0]
                print(f"📊 清空后 {table} 表: {count} 条记录")
            except Exception as e:
                print(f"❌ 检查表 {table} 失败: {e}")
        
        session.close()
        print("\n🎉 MySQL数据库清空完成")
        return True
        
    except Exception as e:
        print(f"❌ 清空MySQL数据异常: {e}")
        return False

def clear_cache_files():
    """清空缓存文件"""
    print("\n🗑️ 清空缓存文件...")
    
    cache_files = [
        "shuimu-admin/data/cache.json",
        "shuimu-admin/data/test_cache.json"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
                print(f"✅ 删除缓存文件: {cache_file}")
            except Exception as e:
                print(f"❌ 删除缓存文件失败 {cache_file}: {e}")
        else:
            print(f"📄 缓存文件不存在: {cache_file}")

def main():
    """主函数"""
    print("🚀 开始清空本地数据...")
    print("=" * 50)
    
    # 清空缓存文件
    clear_cache_files()
    
    # 清空MySQL数据
    mysql_ok = clear_mysql_data()
    
    print("=" * 50)
    if mysql_ok:
        print("🎉 本地数据清空完成！")
        print("\n✅ 已清空:")
        print("  - MySQL数据库中的所有业务数据")
        print("  - JSON缓存文件")
        print("\n💡 下次启动管理端时:")
        print("  - 系统将显示空数据")
        print("  - 或从服务端重新获取真实数据")
    else:
        print("❌ 数据清空失败，请检查MySQL连接")

if __name__ == "__main__":
    main()

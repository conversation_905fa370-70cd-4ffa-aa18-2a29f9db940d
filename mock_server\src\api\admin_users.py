import json
from fastapi import APIRouter, HTTPException
from typing import Optional, Dict, Any
from pydantic import BaseModel
from pathlib import Path

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
        return json.load(f)

# Helper function to save data
def save_data(file_path, data):
    with open(DATA_DIR / file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

# Request models for user data updates
class UserUpdateRequest(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    isActive: Optional[bool] = None

class UserPurchaseUpdateRequest(BaseModel):
    status: Optional[str] = None  # completed, pending, refunded
    price: Optional[float] = None
    type: Optional[str] = None    # 谨慎修改
    itemId: Optional[str] = None  # 谨慎修改
    paymentMethod: Optional[str] = None  # 谨慎修改

class UserProgressUpdateRequest(BaseModel):
    position: Optional[int] = None
    progress: Optional[float] = None
    watchCount: Optional[int] = None
    isCompleted: Optional[bool] = None
    lastWatchedAt: Optional[str] = None  # 谨慎修改

class UserCacheUpdateRequest(BaseModel):
    isCached: Optional[bool] = None
    localPath: Optional[str] = None
    fileSize: Optional[int] = None
    cachedAt: Optional[str] = None  # 谨慎修改

class UserFavoriteUpdateRequest(BaseModel):
    type: Optional[str] = None    # video, category, series
    itemId: Optional[str] = None
    favoritedAt: Optional[str] = None  # 谨慎修改

class UserSettingsUpdateRequest(BaseModel):
    autoPlay: Optional[bool] = None
    playbackSpeed: Optional[float] = None
    subtitles: Optional[bool] = None
    downloadQuality: Optional[str] = None
    notifications: Optional[Dict[str, bool]] = None

@router.put("/admin/users/{user_id}")
def update_user(user_id: str, update_data: UserUpdateRequest):
    """Update user basic information"""
    try:
        # Load users data
        users_db = load_data("users.json")
        
        # Find the user to update
        user_index = None
        for i, user in enumerate(users_db):
            if user["id"] == user_id:
                user_index = i
                break
        
        if user_index is None:
            raise HTTPException(status_code=404, detail=f"User with id '{user_id}' not found")
        
        # Update the user data
        user_data = users_db[user_index].copy()
        
        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            user_data[key] = value
        
        # Update in memory
        users_db[user_index] = user_data
        
        # Save to file
        save_data("users.json", users_db)
        
        # Return updated user
        return {
            "success": True,
            "message": f"User '{user_id}' updated successfully",
            "data": user_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update User Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.put("/admin/users/{user_id}/purchases/{purchase_id}")
def update_user_purchase(user_id: str, purchase_id: str, update_data: UserPurchaseUpdateRequest):
    """Update user purchase record"""
    try:
        # Load user purchases data
        purchases_db = load_data("user_purchases.json")
        
        # Find the user's purchases
        user_purchases = None
        user_index = None
        for i, user_purchase in enumerate(purchases_db):
            if user_purchase["userId"] == user_id:
                user_purchases = user_purchase
                user_index = i
                break
        
        if user_purchases is None:
            raise HTTPException(status_code=404, detail=f"No purchases found for user '{user_id}'")
        
        # Find the specific purchase
        purchase_index = None
        for i, purchase in enumerate(user_purchases["purchases"]):
            if purchase["id"] == purchase_id:
                purchase_index = i
                break
        
        if purchase_index is None:
            raise HTTPException(status_code=404, detail=f"Purchase with id '{purchase_id}' not found")
        
        # Update the purchase data
        purchase_data = user_purchases["purchases"][purchase_index].copy()
        
        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            purchase_data[key] = value
        
        # Update in memory
        purchases_db[user_index]["purchases"][purchase_index] = purchase_data
        
        # Save to file
        save_data("user_purchases.json", purchases_db)
        
        # Return updated purchase
        return {
            "success": True,
            "message": f"Purchase '{purchase_id}' for user '{user_id}' updated successfully",
            "data": purchase_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Purchase Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.put("/admin/users/{user_id}/progress/{video_id}")
def update_user_progress(user_id: str, video_id: str, update_data: UserProgressUpdateRequest):
    """Update user video progress"""
    try:
        # Load user progress data
        progress_db = load_data("user_progress.json")
        
        # Find the user's progress
        user_progress = None
        user_index = None
        for i, user_prog in enumerate(progress_db):
            if user_prog["userId"] == user_id:
                user_progress = user_prog
                user_index = i
                break
        
        if user_progress is None:
            raise HTTPException(status_code=404, detail=f"No progress found for user '{user_id}'")
        
        # Find the specific video progress
        video_index = None
        for i, video_prog in enumerate(user_progress["videoProgress"]):
            if video_prog["videoId"] == video_id:
                video_index = i
                break
        
        if video_index is None:
            raise HTTPException(status_code=404, detail=f"No progress found for video '{video_id}'")
        
        # Update the progress data
        progress_data = user_progress["videoProgress"][video_index].copy()
        
        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            progress_data[key] = value
        
        # Update in memory
        progress_db[user_index]["videoProgress"][video_index] = progress_data
        
        # Save to file
        save_data("user_progress.json", progress_db)
        
        # Return updated progress
        return {
            "success": True,
            "message": f"Progress for video '{video_id}' of user '{user_id}' updated successfully",
            "data": progress_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Progress Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.put("/admin/users/{user_id}/cache/{video_id}")
def update_user_cache(user_id: str, video_id: str, update_data: UserCacheUpdateRequest):
    """Update user video cache status"""
    try:
        # Load user cache data
        cache_db = load_data("user_cache.json")

        # Find the user's cache
        user_cache = None
        user_index = None
        for i, user_c in enumerate(cache_db):
            if user_c["userId"] == user_id:
                user_cache = user_c
                user_index = i
                break

        if user_cache is None:
            raise HTTPException(status_code=404, detail=f"No cache found for user '{user_id}'")

        # Find the specific video cache
        video_index = None
        for i, video_cache in enumerate(user_cache["cachedVideos"]):
            if video_cache["videoId"] == video_id:
                video_index = i
                break

        if video_index is None:
            raise HTTPException(status_code=404, detail=f"No cache found for video '{video_id}'")

        # Update the cache data
        cache_data = user_cache["cachedVideos"][video_index].copy()

        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            cache_data[key] = value

        # Update in memory
        cache_db[user_index]["cachedVideos"][video_index] = cache_data

        # Save to file
        save_data("user_cache.json", cache_db)

        # Return updated cache
        return {
            "success": True,
            "message": f"Cache for video '{video_id}' of user '{user_id}' updated successfully",
            "data": cache_data
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Cache Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.put("/admin/users/{user_id}/favorites/{item_id}")
def update_user_favorite(user_id: str, item_id: str, update_data: UserFavoriteUpdateRequest):
    """Update user favorite item"""
    try:
        # Load user favorites data
        favorites_db = load_data("user_favorites.json")

        # Find the user's favorites
        user_favorites = None
        user_index = None
        for i, user_fav in enumerate(favorites_db):
            if user_fav["userId"] == user_id:
                user_favorites = user_fav
                user_index = i
                break

        if user_favorites is None:
            raise HTTPException(status_code=404, detail=f"No favorites found for user '{user_id}'")

        # Find the specific favorite
        favorite_index = None
        for i, favorite in enumerate(user_favorites["favorites"]):
            if favorite["itemId"] == item_id:
                favorite_index = i
                break

        if favorite_index is None:
            raise HTTPException(status_code=404, detail=f"No favorite found for item '{item_id}'")

        # Update the favorite data
        favorite_data = user_favorites["favorites"][favorite_index].copy()

        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            favorite_data[key] = value

        # Update in memory
        favorites_db[user_index]["favorites"][favorite_index] = favorite_data

        # Save to file
        save_data("user_favorites.json", favorites_db)

        # Return updated favorite
        return {
            "success": True,
            "message": f"Favorite item '{item_id}' for user '{user_id}' updated successfully",
            "data": favorite_data
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Favorite Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

@router.put("/admin/users/{user_id}/settings")
def update_user_settings(user_id: str, update_data: UserSettingsUpdateRequest):
    """Update user settings"""
    try:
        # Load user settings data
        settings_db = load_data("user_settings.json")

        # Find the user's settings
        user_settings = None
        user_index = None
        for i, user_set in enumerate(settings_db):
            if user_set["userId"] == user_id:
                user_settings = user_set
                user_index = i
                break

        if user_settings is None:
            raise HTTPException(status_code=404, detail=f"No settings found for user '{user_id}'")

        # Update the settings data
        settings_data = user_settings["settings"].copy()

        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            if key == "notifications" and isinstance(value, dict):
                # Handle nested notifications object
                if "notifications" not in settings_data:
                    settings_data["notifications"] = {}
                settings_data["notifications"].update(value)
            else:
                settings_data[key] = value

        # Update in memory
        settings_db[user_index]["settings"] = settings_data

        # Save to file
        save_data("user_settings.json", settings_db)

        # Return updated settings
        return {
            "success": True,
            "message": f"Settings for user '{user_id}' updated successfully",
            "data": settings_data
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Settings Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

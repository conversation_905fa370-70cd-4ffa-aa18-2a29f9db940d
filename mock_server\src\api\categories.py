import json
from fastapi import APIRouter, HTTPException, Header
from typing import List, Optional
from pydantic import BaseModel
from ..models.video import Video
from ..utils.user_data import (
    get_video_watch_progress,
    get_video_cache_status,
    get_user_purchases,
    calculate_category_progress,
    calculate_category_watch_count
)
from pathlib import Path

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
        return json.load(f)

# Helper function to save data
def save_data(file_path, data):
    with open(DATA_DIR / file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

# Request model for category updates
class CategoryUpdateRequest(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    isFree: Optional[bool] = None
    order_index: Optional[int] = None
    seriesId: Optional[str] = None  # 可以改变分类归属

videos_db = load_data("videos.json")
categories_db = load_data("categories.json")

def get_video_by_id(video_id, user_id: str = None):
    """获取视频信息，包含用户个人化数据"""
    video_data = next((video for video in videos_db if video["id"] == video_id), None)
    if not video_data:
        return None
    
    # 创建副本，避免修改原始数据
    video_with_user_data = video_data.copy()
    
    if user_id:
        # 添加用户个人化字段
        progress_data = get_video_watch_progress(user_id, video_id)
        cache_data = get_video_cache_status(user_id, video_id)
        
        video_with_user_data["watch_count"] = progress_data.get("watchCount", 0)
        video_with_user_data["progress"] = (
            progress_data.get("position", 0) / progress_data.get("duration", 1) 
            if progress_data.get("duration", 0) > 0 else 0.0
        )
        video_with_user_data["cache_status"] = (
            "CACHED" if cache_data.get("isCached", False) else "NOT_CACHED"
        )
    else:
        # 未登录用户的默认值
        video_with_user_data["watch_count"] = 0
        video_with_user_data["progress"] = 0.0
        video_with_user_data["cache_status"] = "NOT_CACHED"
    
    return video_with_user_data

@router.get("/categories")
def get_all_categories(x_user_id: Optional[str] = Header("user_001")):
    """
    Returns a list of all categories with user-specific data.
    """
    user_purchases = get_user_purchases(x_user_id) if x_user_id else []
    
    categories_with_user_data = []
    for category_data in categories_db:
        # 创建副本，添加用户个人化数据
        category_with_user_data = category_data.copy()
        
        # Check if category is purchased
        category_id = category_data["id"]
        series_id = category_data.get("seriesId")
        is_purchased = (
            category_id in user_purchases or 
            series_id in user_purchases or
            category_data.get("isFree", False)
        )
        category_with_user_data["isPurchased"] = is_purchased
        
        # 计算用户个人化的进度和观看数据
        if x_user_id and (is_purchased or category_data.get("isFree", False)):
            video_ids = category_data.get("videoIds", [])
            category_with_user_data["progress"] = calculate_category_progress(x_user_id, video_ids)
            category_with_user_data["watchCount"] = calculate_category_watch_count(x_user_id, video_ids)
        else:
            # 未购买或未登录的默认值
            category_with_user_data["progress"] = 0.0
            category_with_user_data["watchCount"] = 0
        
        categories_with_user_data.append(category_with_user_data)
    
    return categories_with_user_data

@router.get("/categories/{category_id}/videos", response_model=List[Video])
def get_videos_for_category(category_id: str, x_user_id: Optional[str] = Header("user_001")):
    """
    Returns a list of videos for a specific category with user-specific data.
    """
    category_data = next((cat for cat in categories_db if cat["id"] == category_id), None)
    if not category_data:
        raise HTTPException(status_code=404, detail="Category not found")
        
    video_ids = category_data.get("videoIds", [])
    videos = []
    
    for vid in video_ids:
        video = get_video_by_id(vid, x_user_id)
        if video:
            videos.append(Video(**video))
    
    if not videos:
        raise HTTPException(status_code=404, detail="No videos found for this category")

    return videos

@router.put("/categories/{category_id}")
def update_category(category_id: str, update_data: CategoryUpdateRequest):
    """Update a category by ID"""
    global categories_db

    try:
        # Find the category to update
        category_index = None
        for i, category in enumerate(categories_db):
            if category["id"] == category_id:
                category_index = i
                break

        if category_index is None:
            raise HTTPException(status_code=404, detail=f"Category with id '{category_id}' not found")

        # Update the category data
        category_data = categories_db[category_index].copy()

        # Apply updates only for provided fields
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            category_data[key] = value

        # Update in memory
        categories_db[category_index] = category_data

        # Save to file
        save_data("categories.json", categories_db)

        # Return updated category
        return {
            "success": True,
            "message": f"Category '{category_id}' updated successfully",
            "data": category_data
        }

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_detail = f"Update Category Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)

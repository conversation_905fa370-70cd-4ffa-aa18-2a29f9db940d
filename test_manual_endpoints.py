#!/usr/bin/env python3
"""
手动指定服务器地址测试新的API端点
使用方法: python test_manual_endpoints.py [服务器地址]
例如: python test_manual_endpoints.py http://localhost:3000
"""

import requests
import sys

def test_new_user_endpoints(base_url):
    """测试新的用户数据端点"""
    print(f"🧪 测试新的用户数据端点 - 服务器: {base_url}")
    print("=" * 60)
    
    # 测试用户进度端点
    print("1. 测试用户进度端点")
    try:
        url = f"{base_url}/api/users/user_001/progress/video_001"
        headers = {
            "X-User-Id": "user_001",
            "Content-Type": "application/json"
        }
        data = {"position": 120, "progress": 25.5}
        
        response = requests.put(url, headers=headers, json=data, timeout=5)
        print(f"PUT {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result.get('message', 'OK')}")
        elif response.status_code == 404:
            print("❌ 404 - 端点不存在（可能需要重启服务器）")
        else:
            print(f"⚠️ {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"💥 异常: {e}")
    
    print("-" * 50)
    
    # 测试用户设置端点
    print("2. 测试用户设置端点")
    try:
        url = f"{base_url}/api/users/user_001/settings"
        headers = {
            "X-User-Id": "user_001",
            "Content-Type": "application/json"
        }
        data = {"autoPlay": True, "playbackSpeed": 1.5}
        
        response = requests.put(url, headers=headers, json=data, timeout=5)
        print(f"PUT {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 成功: {result.get('message', 'OK')}")
        elif response.status_code == 404:
            print("❌ 404 - 端点不存在（可能需要重启服务器）")
        else:
            print(f"⚠️ {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"💥 异常: {e}")
    
    print("-" * 50)
    
    # 测试管理员权限
    print("3. 测试管理员权限")
    try:
        url = f"{base_url}/api/users/user_001/progress/video_001"
        headers = {
            "X-User-Id": "admin_001",
            "X-Is-Admin": "true",
            "Content-Type": "application/json"
        }
        data = {"position": 180}
        
        response = requests.put(url, headers=headers, json=data, timeout=5)
        print(f"PUT {url} (管理员)")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 管理员权限正常: {result.get('message', 'OK')}")
        elif response.status_code == 404:
            print("❌ 404 - 端点不存在（可能需要重启服务器）")
        else:
            print(f"⚠️ {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"💥 异常: {e}")
    
    print("-" * 50)

def test_admin_endpoints(base_url):
    """测试管理端专用端点"""
    print(f"🛠️ 测试管理端专用端点 - 服务器: {base_url}")
    print("=" * 60)
    
    # 测试管理端系列端点
    print("1. 测试管理端系列端点")
    try:
        url = f"{base_url}/api/admin/series?page=1&page_size=3"
        headers = {
            "X-User-Id": "admin_001",
            "X-Is-Admin": "true"
        }
        
        response = requests.get(url, headers=headers, timeout=5)
        print(f"GET {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            data_count = len(result.get('data', []))
            print(f"✅ 成功: 获取到 {data_count} 个系列")
        elif response.status_code == 404:
            print("❌ 404 - 端点不存在")
        else:
            print(f"⚠️ {response.status_code}: {response.text[:200]}")
            
    except Exception as e:
        print(f"💥 异常: {e}")
    
    print("-" * 50)

def check_api_docs(base_url):
    """检查API文档"""
    print(f"📚 检查API文档 - 服务器: {base_url}")
    print("=" * 60)
    
    try:
        url = f"{base_url}/docs"
        response = requests.get(url, timeout=5)
        print(f"GET {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API文档可访问")
            print(f"💡 请访问 {url} 查看所有可用端点")
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"💥 异常: {e}")
    
    print("-" * 50)

def main():
    """主函数"""
    # 获取服务器地址
    if len(sys.argv) > 1:
        base_url = sys.argv[1].rstrip('/')
    else:
        print("请输入你的服务器地址（例如: http://localhost:3000）:")
        base_url = input().strip().rstrip('/')
        if not base_url:
            base_url = "http://localhost:8000"
    
    print(f"🚀 开始测试API端点架构")
    print(f"🎯 服务器地址: {base_url}")
    print("=" * 60)
    
    # 先检查基本连接
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        print("继续测试端点...")
    
    print()
    
    # 执行测试
    test_new_user_endpoints(base_url)
    test_admin_endpoints(base_url)
    check_api_docs(base_url)
    
    print("🎉 测试完成")
    print("\n💡 如果看到404错误，说明新端点还没有部署")
    print("解决方法:")
    print("1. 重启服务器: uvicorn src.main:app --reload")
    print("2. 检查服务器日志是否有错误")
    print(f"3. 访问API文档: {base_url}/docs")

if __name__ == "__main__":
    main()

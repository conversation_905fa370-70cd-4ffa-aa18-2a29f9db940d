#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无分页API测试脚本
验证API层不再进行分页处理
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

from api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient

def test_no_pagination_api():
    """测试无分页API"""
    print("🧪 测试API层无分页处理...")
    print("=" * 60)
    
    try:
        # 创建API客户端
        api_client = APIClient("http://localhost:8000")
        
        # 测试分类API
        print("📂 测试分类API（无分页）...")
        category_client = CategoryAPIClient(api_client)
        category_result = category_client.get_categories(page=1, page_size=100)
        
        print(f"分类API结果:")
        print(f"  success: {category_result.get('success')}")
        print(f"  数据数量: {len(category_result.get('data', []))}")
        print(f"  分页信息: {category_result.get('pagination', {})}")
        
        if category_result.get('data'):
            print(f"  第一个分类: {category_result['data'][0].get('title')}")
        
        # 测试系列API
        print("\n📚 测试系列API（无分页）...")
        series_client = SeriesAPIClient(api_client)
        series_result = series_client.get_series(page=1, page_size=100)
        
        print(f"系列API结果:")
        print(f"  success: {series_result.get('success')}")
        print(f"  数据数量: {len(series_result.get('data', []))}")
        print(f"  分页信息: {series_result.get('pagination', {})}")
        
        # 测试视频API
        print("\n📹 测试视频API（无分页）...")
        video_client = VideoAPIClient(api_client)
        video_result = video_client.get_videos(page=1, page_size=100)
        
        print(f"视频API结果:")
        print(f"  success: {video_result.get('success')}")
        print(f"  数据数量: {len(video_result.get('data', []))}")
        print(f"  分页信息: {video_result.get('pagination', {})}")
        
        # 验证结果
        category_count = len(category_result.get('data', []))
        series_count = len(series_result.get('data', []))
        video_count = len(video_result.get('data', []))
        
        print(f"\n📊 数据统计:")
        print(f"  分类: {category_count} 个 (期望: 11)")
        print(f"  系列: {series_count} 个 (期望: 4)")
        print(f"  视频: {video_count} 个 (期望: 20)")
        
        # 检查是否达到预期
        success = (
            category_count >= 10 and  # 至少10个分类
            series_count >= 3 and     # 至少3个系列
            video_count >= 15         # 至少15个视频
        )
        
        if success:
            print("\n🎉 无分页API测试通过！")
            print("✅ API层不再进行分页处理")
            print("✅ 返回了服务端的完整数据")
            return True
        else:
            print("\n❌ 数据仍然不完整")
            print("⚠️ 可能还有其他问题需要解决")
            return False
            
    except Exception as e:
        print(f"❌ 无分页API测试异常: {e}")
        return False

def test_direct_api_call():
    """直接测试API调用"""
    print("\n🧪 直接测试API调用...")
    
    try:
        import requests
        
        # 直接调用分类API
        response = requests.get("http://localhost:8000/api/categories", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"📂 直接调用 /api/categories:")
            print(f"  状态码: {response.status_code}")
            print(f"  数据类型: {type(data)}")
            print(f"  数据数量: {len(data) if isinstance(data, list) else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print(f"  第一个分类: {data[0]}")
            
            return len(data) if isinstance(data, list) else 0
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return 0
            
    except Exception as e:
        print(f"❌ 直接API调用异常: {e}")
        return 0

def main():
    """主测试函数"""
    print("🚀 开始无分页API测试...")
    
    # 直接测试API调用
    direct_count = test_direct_api_call()
    
    # 测试API客户端
    api_ok = test_no_pagination_api()
    
    print("=" * 60)
    print("🎯 测试结果总结:")
    print(f"  直接API调用: 返回 {direct_count} 个分类")
    print(f"  API客户端: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if api_ok and direct_count >= 10:
        print("\n🎉 无分页API修复成功！")
        print("\n✅ 修复效果:")
        print("  1. API层不再进行分页处理 ✅")
        print("  2. 返回服务端的完整数据 ✅")
        print("  3. 分页逻辑移到界面层处理 ✅")
        
        print("\n🚀 现在管理端应该能够:")
        print("  - 显示所有11个分类")
        print("  - 显示所有20个视频")
        print("  - 显示4个系列")
        print("  - 在界面层进行分页显示")
    else:
        print("\n⚠️ 修复效果需要进一步验证")
        if direct_count < 10:
            print("❌ 服务端API可能有问题")
        if not api_ok:
            print("❌ API客户端处理有问题")

if __name__ == "__main__":
    main()

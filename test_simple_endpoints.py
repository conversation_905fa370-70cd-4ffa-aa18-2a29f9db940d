#!/usr/bin/env python3
"""
简单测试新的API端点
直接测试端点，不检查服务器连接
"""

import requests
import json

def test_endpoint(method, url, headers=None, data=None):
    """测试单个端点"""
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=5)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data, timeout=5)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=5)
        
        print(f"{method} {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            try:
                result = response.json()
                print(f"响应: {result.get('message', 'OK')}")
            except:
                print("响应: 非JSON格式")
        elif response.status_code == 404:
            print("❌ 404 - 端点不存在")
        elif response.status_code == 405:
            print("⚠️ 405 - 方法不允许（端点存在但方法错误）")
        elif response.status_code == 403:
            print("🔒 403 - 权限不足")
        elif response.status_code == 401:
            print("🔑 401 - 需要认证")
        else:
            print(f"⚠️ {response.status_code} - {response.text[:100]}")
        
        print("-" * 50)
        return response.status_code
        
    except requests.exceptions.ConnectionError:
        print(f"💥 连接失败: {url}")
        print("请检查服务器是否启动，或尝试不同的地址")
        print("-" * 50)
        return None
    except Exception as e:
        print(f"💥 异常: {e}")
        print("-" * 50)
        return None

def main():
    """主测试函数"""
    print("🧪 简单端点测试")
    print("=" * 60)
    
    # 可能的服务器地址
    base_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://0.0.0.0:8000"
    ]
    
    # 测试端点列表
    test_cases = [
        # 基础端点
        ("GET", "/", None, None),
        ("GET", "/docs", None, None),
        
        # 现有端点
        ("GET", "/api/series", None, None),
        ("GET", "/api/categories", None, None),
        
        # 管理端端点
        ("GET", "/api/admin/series", {"X-User-Id": "admin_001", "X-Is-Admin": "true"}, None),
        
        # 新的用户数据端点
        ("PUT", "/api/users/user_001/progress/video_001", 
         {"X-User-Id": "user_001", "Content-Type": "application/json"}, 
         {"position": 120}),
        
        ("PUT", "/api/users/user_001/settings", 
         {"X-User-Id": "user_001", "Content-Type": "application/json"}, 
         {"autoPlay": True}),
        
        ("PUT", "/api/users/user_001/cache/video_001", 
         {"X-User-Id": "user_001", "Content-Type": "application/json"}, 
         {"isCached": True}),
        
        # 管理员操作用户数据
        ("PUT", "/api/users/user_001/progress/video_001", 
         {"X-User-Id": "admin_001", "X-Is-Admin": "true", "Content-Type": "application/json"}, 
         {"position": 180}),
    ]
    
    # 尝试每个服务器地址
    working_url = None
    for base_url in base_urls:
        print(f"🔍 尝试服务器地址: {base_url}")
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                working_url = base_url
                print(f"✅ 服务器响应正常: {base_url}")
                break
        except:
            print(f"❌ 无法连接: {base_url}")
    
    if not working_url:
        print("⚠️ 无法找到可用的服务器，使用默认地址进行测试")
        working_url = base_urls[0]
    
    print(f"\n🎯 使用服务器地址: {working_url}")
    print("=" * 60)
    
    # 执行测试
    success_count = 0
    total_count = 0
    
    for method, endpoint, headers, data in test_cases:
        url = working_url + endpoint
        status_code = test_endpoint(method, url, headers, data)
        
        total_count += 1
        if status_code and status_code < 400:
            success_count += 1
    
    # 测试总结
    print("=" * 60)
    print("🎉 测试完成")
    print(f"📊 成功: {success_count}/{total_count}")
    
    if success_count == 0:
        print("\n❌ 所有测试都失败了")
        print("可能的原因:")
        print("1. 服务器未启动")
        print("2. 服务器运行在不同的端口")
        print("3. 新的端点尚未部署")
        print("\n💡 建议:")
        print("1. 检查服务器启动日志")
        print("2. 访问 API 文档: http://localhost:8000/docs")
        print("3. 重启服务器: uvicorn src.main:app --reload")
    elif success_count < total_count:
        print(f"\n⚠️ 部分测试失败 ({total_count - success_count} 个)")
        print("这可能是正常的，因为新端点可能需要重启服务器")
    else:
        print("\n✅ 所有测试都成功了！")
        print("新的API端点架构工作正常")

if __name__ == "__main__":
    main()

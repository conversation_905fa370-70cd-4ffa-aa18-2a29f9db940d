#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器
负责本地数据缓存的读写和管理
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class CacheManager:
    """本地缓存管理器"""
    
    def __init__(self, cache_file_path: str = "./data/cache.json"):
        self.cache_file = Path(cache_file_path)
        self.data = {}
        self.cache_expire_hours = 24  # 缓存过期时间（小时）
        
        # 确保缓存目录存在
        self.cache_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化时加载缓存
        self.load_cache()
    
    def load_cache(self) -> bool:
        """加载本地缓存"""
        try:
            if not self.cache_file.exists():
                logger.info("缓存文件不存在，将创建新的缓存")
                return False
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查缓存版本和有效性
            if not self._is_cache_valid(cache_data):
                logger.info("缓存已过期或无效，需要重新获取数据")
                return False
            
            self.data = cache_data.get('data', {})
            logger.info(f"成功加载本地缓存，包含 {len(self.data)} 个数据类型")
            return True
            
        except Exception as e:
            logger.error(f"加载缓存失败: {e}")
            return False
    
    def save_cache(self, data: Dict[str, Any]) -> bool:
        """保存数据到本地缓存"""
        try:
            cache_data = {
                'cache_version': '1.0',
                'last_update': datetime.now().isoformat(),
                'data': data,
                'metadata': {
                    'total_counts': self._calculate_counts(data),
                    'last_sync': datetime.now().isoformat()
                }
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            self.data = data
            logger.info(f"成功保存缓存到 {self.cache_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")
            return False
    
    def get_data(self, data_type: str) -> Optional[Dict[str, Any]]:
        """获取指定类型的缓存数据"""
        return self.data.get(data_type)
    
    def set_data(self, data_type: str, data: Any) -> None:
        """设置指定类型的缓存数据"""
        self.data[data_type] = data
    
    def has_data(self, data_type: str) -> bool:
        """检查是否有指定类型的缓存数据"""
        return data_type in self.data and self.data[data_type] is not None
    
    def clear_cache(self) -> bool:
        """清空缓存"""
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
            self.data = {}
            logger.info("缓存已清空")
            return True
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    def _is_cache_valid(self, cache_data: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        try:
            # 检查缓存版本
            if cache_data.get('cache_version') != '1.0':
                return False
            
            # 检查缓存时间
            last_update_str = cache_data.get('last_update')
            if not last_update_str:
                return False
            
            last_update = datetime.fromisoformat(last_update_str)
            expire_time = last_update + timedelta(hours=self.cache_expire_hours)
            
            if datetime.now() > expire_time:
                logger.info("缓存已过期")
                return False
            
            # 检查数据完整性
            data = cache_data.get('data', {})
            if not data:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查缓存有效性失败: {e}")
            return False
    
    def _calculate_counts(self, data: Dict[str, Any]) -> Dict[str, int]:
        """计算各类型数据的数量"""
        counts = {}
        for data_type, items in data.items():
            if isinstance(items, list):
                counts[f"{data_type}_count"] = len(items)
            elif isinstance(items, dict) and 'data' in items:
                counts[f"{data_type}_count"] = len(items['data'])
        return counts
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            if not self.cache_file.exists():
                return {'exists': False}
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            return {
                'exists': True,
                'last_update': cache_data.get('last_update'),
                'cache_version': cache_data.get('cache_version'),
                'data_types': list(cache_data.get('data', {}).keys()),
                'metadata': cache_data.get('metadata', {}),
                'file_size': self.cache_file.stat().st_size,
                'is_valid': self._is_cache_valid(cache_data)
            }
        except Exception as e:
            logger.error(f"获取缓存信息失败: {e}")
            return {'exists': False, 'error': str(e)}

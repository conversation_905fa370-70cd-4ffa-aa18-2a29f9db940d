<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="228478ff-15cd-4d9c-ac6c-51e5f799bbf2" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/shuimu-admin/check_db_status.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/migrate_db.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/test_migration.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/update_db_structure.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/_cffi_backend.cp312-win_amd64.pyd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/INSTALLER" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/METADATA" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/RECORD" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/WHEEL" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/entry_points.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi-1.17.1.dist-info/top_level.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/_cffi_errors.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/_cffi_include.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/_embedding.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/_imp_emulation.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/_shimmed_dist_utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/api.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/backend_ctypes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/cffi_opcode.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/commontypes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/cparser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/error.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/ffiplatform.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/lock.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/model.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/parse_c_type.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/pkgconfig.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/recompiler.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/setuptools_ext.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/vengine_cpy.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/vengine_gen.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cffi/verifier.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/INSTALLER" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/METADATA" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/RECORD" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/REQUESTED" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/WHEEL" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.APACHE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography-45.0.4.dist-info/licenses/LICENSE.BSD" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/__about__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/exceptions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/fernet.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/_oid.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/backends/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/backends/openssl/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/backends/openssl/backend.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust.pyd" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/__init__.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/_openssl.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/asn1.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/exceptions.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/ocsp.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/__init__.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/aead.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ciphers.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/cmac.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/dh.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/dsa.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ec.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ed25519.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/ed448.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/hashes.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/hmac.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/kdf.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/keys.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/poly1305.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/rsa.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/x25519.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/openssl/x448.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/pkcs12.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/pkcs7.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/test_support.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/_rust/x509.pyi" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/openssl/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/openssl/_conditional.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/bindings/openssl/binding.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/decrepit/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/decrepit/ciphers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/decrepit/ciphers/algorithms.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/_asymmetric.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/_cipheralgorithm.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/_serialization.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/dh.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/dsa.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ec.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ed25519.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/ed448.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/padding.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/rsa.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/types.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/x25519.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/asymmetric/x448.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/ciphers/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/ciphers/aead.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/ciphers/algorithms.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/ciphers/base.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/ciphers/modes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/cmac.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/constant_time.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/hashes.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/hmac.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/argon2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/concatkdf.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/hkdf.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/kbkdf.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/pbkdf2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/scrypt.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/kdf/x963kdf.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/keywrap.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/padding.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/poly1305.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/serialization/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/serialization/base.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/serialization/pkcs12.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/serialization/pkcs7.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/serialization/ssh.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/twofactor/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/twofactor/hotp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/hazmat/primitives/twofactor/totp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/py.typed" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/base.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/certificate_transparency.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/extensions.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/general_name.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/name.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/ocsp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/oid.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/cryptography/x509/verification.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/INSTALLER" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/METADATA" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/RECORD" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/WHEEL" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser-2.22.dist-info/top_level.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/_ast_gen.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/_build_tables.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/_c_ast.cfg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ast_transforms.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/c_ast.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/c_generator.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/c_lexer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/c_parser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/lextab.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/cpp.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/ctokens.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/lex.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/yacc.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/ply/ygen.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/plyparser.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/shuimu-admin/venv/Lib/site-packages/pycparser/yacctab.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/mock_server/src/api/user_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/mock_server/src/api/user_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/migrate_database.py" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/migrate_database.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/src/api/client.py" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/src/api/client.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/src/database/dao.py" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/src/database/dao.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/src/database/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/src/database/models.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/shuimu-admin/src/services/course_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/shuimu-admin/src/services/course_service.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test_new_api_architecture.py" beforeDir="false" afterPath="$PROJECT_DIR$/test_new_api_architecture.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=LocalEmulator, isTemplate=false, identifier=path=C:\Users\<USER>\.android\avd\Medium_Phone_API_35.avd)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ShuimuVideoCourse" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ykjT3d6bQnqxlDGvseYDddXDO2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Android App.MainActivity (1).executor&quot;: &quot;Run&quot;,
    &quot;Android App.MainActivity.executor&quot;: &quot;Run&quot;,
    &quot;Android App.ShuimuVideoCourse.app.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;deletionFromPopupRequiresConfirmation&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;fenzhi03&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/MCP_Server&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.completion&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="MainActivity" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false" temporary="true">
      <module name="ShuimuVideoCourse.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="specific_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="com.shuimu.course.MainActivity" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Android App.MainActivity" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-975db3bf15a3-31b6be0877a2-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-241.18034.82" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="228478ff-15cd-4d9c-ac6c-51e5f799bbf2" name="Changes" comment="" />
      <created>1750385469490</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750385469490</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <ignored-roots>
      <path value="$PROJECT_DIR$/.." />
    </ignored-roots>
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.shuimu.course">
          <value>
            <CheckInfo lastCheckTimestamp="1751168591391" />
          </value>
        </entry>
        <entry key="com.shuimu.course.test">
          <value>
            <CheckInfo lastCheckTimestamp="1751168591391" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试服务端API
"""

import requests
import json

def test_server():
    """测试服务端"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试服务端API...")
    
    # 测试根路径
    try:
        response = requests.get(base_url, timeout=5)
        print(f"根路径: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"根路径异常: {e}")
        return False
    
    # 测试docs路径
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"文档路径: {response.status_code}")
    except Exception as e:
        print(f"文档路径异常: {e}")
    
    # 测试各种可能的API路径
    test_paths = [
        "/api/admin/series",
        "/api/admin/categories", 
        "/api/admin/videos",
        "/api/series",
        "/api/categories",
        "/api/videos"
    ]
    
    for path in test_paths:
        try:
            url = f"{base_url}{path}?page=1&page_size=2"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {path} -> 200 OK, 类型: {type(data)}, 长度: {len(data) if isinstance(data, list) else 'N/A'}")
                except:
                    print(f"✅ {path} -> 200 OK, 非JSON响应")
            else:
                print(f"❌ {path} -> {response.status_code}: {response.text[:50]}")
                
        except Exception as e:
            print(f"💥 {path} -> 异常: {e}")
    
    return True

if __name__ == "__main__":
    test_server()

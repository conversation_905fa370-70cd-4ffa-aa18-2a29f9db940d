#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
测试管理端API客户端是否能正确连接服务端
"""

import sys
import os
import logging

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from api.client import APIClient, CategoryAPIClient, SeriesAPIClient, VideoAPIClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_basic_connection():
    """测试基础连接"""
    print("🧪 测试基础API连接...")
    
    try:
        client = APIClient("http://localhost:8000")
        
        # 测试根路径
        result = client.get("/")
        print(f"📡 根路径测试: {result}")
        
        if result.get('success', True):
            print("✅ 基础连接正常")
            return True
        else:
            print(f"❌ 基础连接失败: {result}")
            return False
            
    except Exception as e:
        print(f"❌ 基础连接异常: {e}")
        return False

def test_admin_apis():
    """测试管理端API"""
    print("🧪 测试管理端API...")
    
    try:
        # 创建API客户端
        api_client = APIClient("http://localhost:8000")
        series_client = SeriesAPIClient(api_client)
        category_client = CategoryAPIClient(api_client)
        video_client = VideoAPIClient(api_client)
        
        # 测试系列API
        print("📚 测试系列API...")
        series_result = series_client.get_series(page=1, page_size=5)
        print(f"系列API结果: success={series_result.get('success')}, "
              f"data_count={len(series_result.get('data', []))}")
        
        # 测试分类API
        print("📂 测试分类API...")
        category_result = category_client.get_categories(page=1, page_size=5)
        print(f"分类API结果: success={category_result.get('success')}, "
              f"data_count={len(category_result.get('data', []))}")
        
        # 测试视频API
        print("📹 测试视频API...")
        video_result = video_client.get_videos(page=1, page_size=5)
        print(f"视频API结果: success={video_result.get('success')}, "
              f"data_count={len(video_result.get('data', []))}")
        
        # 检查是否至少有一个API成功
        success_count = sum([
            series_result.get('success', False),
            category_result.get('success', False),
            video_result.get('success', False)
        ])
        
        if success_count > 0:
            print(f"✅ 管理端API测试通过 ({success_count}/3 个API成功)")
            return True
        else:
            print("❌ 所有管理端API都失败了")
            return False
            
    except Exception as e:
        print(f"❌ 管理端API测试异常: {e}")
        return False

def test_direct_curl():
    """测试直接curl命令"""
    print("🧪 测试直接curl命令...")
    
    import subprocess
    
    try:
        # 测试根路径
        result = subprocess.run([
            "curl.exe", "-s", "-X", "GET", 
            "http://localhost:8000/",
            "-H", "accept: application/json"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ curl根路径成功: {result.stdout}")
        else:
            print(f"❌ curl根路径失败: {result.stderr}")
            return False
        
        # 测试管理端系列API
        result = subprocess.run([
            "curl.exe", "-s", "-X", "GET", 
            "http://localhost:8000/api/admin/series?page=1&page_size=5",
            "-H", "accept: application/json"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ curl系列API成功: {result.stdout[:100]}...")
            return True
        else:
            print(f"❌ curl系列API失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ curl测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API连接测试...")
    print("=" * 50)
    
    # 测试基础连接
    if test_basic_connection():
        print("✅ 基础连接测试通过")
    else:
        print("❌ 基础连接测试失败")
        return
    
    print("-" * 30)
    
    # 测试直接curl
    if test_direct_curl():
        print("✅ curl测试通过")
    else:
        print("❌ curl测试失败")
    
    print("-" * 30)
    
    # 测试管理端API
    if test_admin_apis():
        print("✅ 管理端API测试通过")
    else:
        print("❌ 管理端API测试失败")
    
    print("=" * 50)
    print("🎉 API连接测试完成！")
    
    print("\n💡 如果API测试失败，可能的原因：")
    print("1. 服务端未启动或端口不是8000")
    print("2. API路径不匹配")
    print("3. 请求格式或参数问题")
    print("4. 网络连接问题")

if __name__ == "__main__":
    main()

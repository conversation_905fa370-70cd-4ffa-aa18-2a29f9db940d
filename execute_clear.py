#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import os

print("🗑️ 开始清空MySQL数据...")

try:
    # 直接连接MySQL
    connection = pymysql.connect(
        host='localhost',
        port=3306,
        user='mike',
        password='dyj217',
        database='shuimu_course',
        charset='utf8mb4'
    )
    
    print("✅ 数据库连接成功")
    
    cursor = connection.cursor()
    
    # 清空各个表的数据
    tables = ['videos', 'categories', 'series', 'users']
    
    for table in tables:
        try:
            # 先查看数据量
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"📊 清空前 {table} 表: {count} 条记录")
            
            # 清空数据
            cursor.execute(f"DELETE FROM {table}")
            print(f"✅ 清空表 {table}")
            
        except Exception as e:
            print(f"❌ 清空表 {table} 失败: {e}")
    
    # 提交事务
    connection.commit()
    print("💾 事务已提交")
    
    # 验证清空结果
    print("\n🔍 验证清空结果:")
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"📊 清空后 {table} 表: {count} 条记录")
        except Exception as e:
            print(f"❌ 检查表 {table} 失败: {e}")
    
    cursor.close()
    connection.close()
    print("\n🎉 MySQL数据库清空完成！")
    
except Exception as e:
    print(f"❌ 清空数据异常: {e}")

# 清空缓存文件
print("\n🗑️ 清空缓存文件...")
cache_files = [
    "shuimu-admin/data/cache.json",
    "shuimu-admin/data/test_cache.json"
]

for cache_file in cache_files:
    if os.path.exists(cache_file):
        try:
            os.remove(cache_file)
            print(f"✅ 删除缓存文件: {cache_file}")
        except Exception as e:
            print(f"❌ 删除缓存文件失败 {cache_file}: {e}")
    else:
        print(f"📄 缓存文件不存在: {cache_file}")

print("\n🎉 本地数据清空完成！")

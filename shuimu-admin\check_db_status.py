#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库状态脚本
"""

import sys
import os
import pymysql

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.config import Config

def check_database_status():
    """检查数据库状态"""
    print("🔍 检查数据库状态...")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = Config()
        db_config = config.get_database_config()
        
        connection = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset']
        )
        
        cursor = connection.cursor()
        
        # 1. 显示所有表
        print("📊 1. 数据库中的所有表:")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        for table in tables:
            print(f"   - {table[0]}")
        
        print()
        
        # 2. 检查每个表的结构
        table_names = [table[0] for table in tables]
        
        for table_name in table_names:
            print(f"📋 2. {table_name} 表结构:")
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            print(f"   {'字段名':<15} {'类型':<20} {'空值':<5} {'键':<5} {'默认值':<10} {'额外':<15}")
            print(f"   {'-'*15} {'-'*20} {'-'*5} {'-'*5} {'-'*10} {'-'*15}")
            
            for column in columns:
                field, type_, null, key, default, extra = column
                print(f"   {field:<15} {type_:<20} {null:<5} {key:<5} {str(default):<10} {extra:<15}")
            
            print()
        
        # 3. 检查外键约束
        print("🔗 3. 外键约束关系:")
        cursor.execute("""
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = %s
            AND REFERENCED_TABLE_NAME IS NOT NULL
        """, (db_config['database'],))
        
        constraints = cursor.fetchall()
        if constraints:
            print(f"   {'表名':<15} {'字段':<15} {'约束名':<20} {'引用表':<15} {'引用字段':<15}")
            print(f"   {'-'*15} {'-'*15} {'-'*20} {'-'*15} {'-'*15}")
            for constraint in constraints:
                table, column, constraint_name, ref_table, ref_column = constraint
                print(f"   {table:<15} {column:<15} {constraint_name:<20} {ref_table:<15} {ref_column:<15}")
        else:
            print("   没有外键约束")
        
        print()
        
        # 4. 检查每个表的数据数量
        print("📈 4. 每个表的数据数量:")
        for table_name in table_names:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   {table_name:<15}: {count} 条记录")
            except Exception as e:
                print(f"   {table_name:<15}: 查询失败 - {e}")
        
        print()
        
        # 5. 特别检查series表的ID字段类型
        print("🎯 5. 重点检查series表ID字段:")
        if 'series' in table_names:
            cursor.execute("SHOW COLUMNS FROM series WHERE Field = 'id'")
            id_column = cursor.fetchone()
            if id_column:
                field, type_, null, key, default, extra = id_column
                print(f"   series.id 字段类型: {type_}")
                
                if 'varchar' in type_.lower():
                    print("   ✅ ID字段已是VARCHAR类型，支持字符串ID")
                elif 'int' in type_.lower():
                    print("   ❌ ID字段仍是INTEGER类型，需要修改")
                else:
                    print(f"   ⚠️ ID字段类型未知: {type_}")
        
        # 6. 检查是否有数据
        print("\n📦 6. 数据内容检查:")
        for table_name in ['series', 'categories', 'videos']:
            if table_name in table_names:
                try:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    rows = cursor.fetchall()
                    if rows:
                        print(f"   {table_name} 表前3条数据:")
                        for i, row in enumerate(rows, 1):
                            print(f"     {i}. {row}")
                    else:
                        print(f"   {table_name} 表: 无数据")
                except Exception as e:
                    print(f"   {table_name} 表: 查询失败 - {e}")
        
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 60)
        print("✅ 数据库状态检查完成")
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 数据库状态检查工具")
    print("🎯 目标：全面检查当前数据库的表结构和数据状态")
    
    check_database_status()

if __name__ == "__main__":
    main()

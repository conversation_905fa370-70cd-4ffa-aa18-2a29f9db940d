#!/usr/bin/env python3
"""
测试导入修复是否成功
"""

import sys
import os

# 添加mock_server到Python路径
sys.path.insert(0, os.path.join(os.getcwd(), 'mock_server'))

def test_user_data_import():
    """测试user_data模块导入"""
    print("🧪 测试user_data模块导入...")
    
    try:
        from src.api import user_data
        print("✅ user_data模块导入成功")
        
        # 检查路由是否存在
        if hasattr(user_data, 'router'):
            print("✅ user_data.router存在")
        else:
            print("❌ user_data.router不存在")
            
        return True
    except Exception as e:
        print(f"❌ user_data模块导入失败: {e}")
        return False

def test_main_import():
    """测试main模块导入"""
    print("\n🧪 测试main模块导入...")
    
    try:
        from src.main import app
        print("✅ main模块导入成功")
        print("✅ FastAPI应用创建成功")
        return True
    except Exception as e:
        print(f"❌ main模块导入失败: {e}")
        return False

def test_utils_import():
    """测试utils模块导入"""
    print("\n🧪 测试utils模块导入...")
    
    try:
        from src.utils.user_data import (
            load_user_data,
            save_user_data,
            get_video_watch_progress,
            update_video_watch_progress,
            get_video_cache_status,
            update_video_cache_status
        )
        print("✅ utils.user_data函数导入成功")
        return True
    except Exception as e:
        print(f"❌ utils.user_data函数导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试导入修复")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查mock_server目录是否存在
    mock_server_dir = os.path.join(current_dir, 'mock_server')
    if os.path.exists(mock_server_dir):
        print("✅ mock_server目录存在")
    else:
        print("❌ mock_server目录不存在")
        return
    
    # 执行测试
    tests = [
        test_utils_import,
        test_user_data_import,
        test_main_import
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎉 测试完成: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("✅ 所有导入测试通过！服务器应该能正常启动")
        print("\n💡 现在可以启动服务器:")
        print("cd mock_server")
        print("uvicorn src.main:app --reload")
    else:
        print("❌ 部分导入测试失败，需要进一步修复")

if __name__ == "__main__":
    main()

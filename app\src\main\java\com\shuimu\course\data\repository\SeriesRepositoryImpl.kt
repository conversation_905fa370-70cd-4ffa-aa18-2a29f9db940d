package com.shuimu.course.data.repository

import android.content.Context
import com.google.gson.Gson
import dagger.hilt.android.qualifiers.ApplicationContext
import com.shuimu.course.data.local.dao.*
import com.shuimu.course.data.local.relations.toDomain
import com.shuimu.course.data.remote.api.SeriesApi
import com.shuimu.course.data.remote.dto.SeriesDto
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toDomainModel
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import com.shuimu.course.data.repository.update.DataDiffDetector
import com.shuimu.course.data.repository.update.IncrementalUpdateStrategy
import com.shuimu.course.data.repository.update.DataChangeNotifier
import androidx.room.withTransaction
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SeriesRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson,
    private val seriesApi: SeriesApi,
    private val database: com.shuimu.course.data.local.database.AppDatabase,
    private val seriesDao: SeriesDao,
    private val categoryDao: CategoryDao,
    private val videoDao: VideoDao,
    private val playProgressDao: PlayProgressDao,
    private val cacheInfoDao: CacheInfoDao,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val dataDiffDetector: DataDiffDetector,
    private val incrementalUpdateStrategy: IncrementalUpdateStrategy,
    private val dataChangeNotifier: DataChangeNotifier
) : SeriesRepository {

    // 🔥 优化：内存缓存机制，符合行业标准的缓存策略
    private var cachedSeriesData: List<Series>? = null
    private var cacheTimestamp: Long = 0
    private var cacheDataSource: String = "未知"

    // 🔥 优化：差异化缓存策略，符合行业标准
    private val seriesDataCacheValidDuration = 4 * 60 * 60 * 1000L // 4小时（首页系列数据）
    private val userStateCacheValidDuration = 24 * 60 * 60 * 1000L // 24小时（用户购买状态）
    // 视频播放进度：永久缓存（在PlayProgressRepository中处理）

    // 🔥 新增：后台更新协程作用域
    private val backgroundScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 🔥 新增：预加载数据状态管理
    private var isPreloadCompleted: Boolean = false
    private var preloadDataSource: String = "未知"

    companion object {
        // 🔥 开发调试：手动控制数据源
        // "AUTO" - 自动选择, "SERVER" - 强制服务器, "LOCAL" - 强制本地, "PRESET" - 强制预置
        private const val DEBUG_DATA_SOURCE = "SERVER"

        /**
         * 获取调试数据源设置
         */
        private fun getDataSourceForDebug(): String {
            return try {
                // 使用反射检查是否为DEBUG模式，避免直接引用BuildConfig
                val buildConfigClass = Class.forName("com.shuimu.course.BuildConfig")
                val debugField = buildConfigClass.getField("DEBUG")
                val isDebug = debugField.getBoolean(null)
                if (isDebug) DEBUG_DATA_SOURCE else "AUTO"
            } catch (e: Exception) {
                // 如果无法获取DEBUG状态，默认使用AUTO模式
                "AUTO"
            }
        }
    }

    override fun getSeries(): Flow<Resource<List<Series>>> = flow {
        // 🔥 调试模式：检查强制数据源设置
        val debugDataSource = getDataSourceForDebug()
        android.util.Log.d("SeriesRepository", "🔧 调试数据源设置: $debugDataSource")

        android.util.Log.d("SeriesRepository", "开始获取系列数据")

        // 🔥 调试模式：强制使用指定数据源
        when (debugDataSource) {
            "PRESET" -> {
                android.util.Log.d("SeriesRepository", "🔧 调试模式：强制使用预置数据")
                val presetData = getPresetData()
                // 🔥 关键：调试模式的预置数据也使用统一价格计算
                val calculatedPresetData = dataLayerManager.recalculateAllSeriesPrices(presetData)
                emit(Resource.Success(calculatedPresetData, "预置数据(调试)"))
                return@flow
            }
            "LOCAL" -> {
                android.util.Log.d("SeriesRepository", "🔧 调试模式：强制使用本地数据")
                val localData = getCombinedLocalData()
                val dataToUse = localData.ifEmpty { getPresetData() }
                // 🔥 关键：调试模式的本地数据也使用统一价格计算
                val calculatedData = dataLayerManager.recalculateAllSeriesPrices(dataToUse)
                val sourceLabel = if (localData.isNotEmpty()) "本地数据(调试)" else "预置数据(本地为空)"
                emit(Resource.Success(calculatedData, sourceLabel))
                return@flow
            }
            "SERVER" -> {
                android.util.Log.d("SeriesRepository", "🔧 调试模式：强制使用服务器数据")
                // 跳过缓存，直接请求服务器
            }
        }

        // 🔥 正确逻辑：并行处理 - 立即返回本地数据，同时请求服务器数据
        android.util.Log.d("SeriesRepository", "🚀 开始并行数据获取流程")

        // 🔥 第一步：并行检查本地数据
        val localData = try {
            getCombinedLocalDataParallel()
        } catch (e: Exception) {
            android.util.Log.w("SeriesRepository", "获取本地数据失败", e)
            emptyList()
        }

        // 🔥 如果有本地数据，立即返回（不等服务器）
        if (localData.isNotEmpty()) {
            android.util.Log.d("SeriesRepository", "✅ 立即使用本地数据: ${localData.size}个系列")
            val recalculatedLocalData = dataLayerManager.recalculateAllSeriesPrices(localData)
            cacheSeriesData(recalculatedLocalData, "本地数据")
            emit(Resource.Success(recalculatedLocalData, "本地数据"))

            // 🔥 后台异步更新服务器数据
            backgroundScope.launch {
                updateServerDataInBackground()
            }
            return@flow
        }

        // 🔥 本地没有数据，等待服务器数据
        android.util.Log.w("SeriesRepository", "本地数据为空，等待服务器数据")

        try {
            android.util.Log.d("SeriesRepository", "请求服务器数据...")
            val remoteSeries = seriesApi.getAllSeries().body()
            if (remoteSeries != null) {
                android.util.Log.d("SeriesRepository", "✅ 服务器数据获取成功: ${remoteSeries.size}个系列")
                val domainSeries = remoteSeries.map { it.toDomainModel() }
                val seriesWithCalculatedPrices = dataLayerManager.recalculateAllSeriesPrices(domainSeries)

                emit(Resource.Success(seriesWithCalculatedPrices, "服务器数据"))

                // 保存到本地数据库
                saveToLocalDatabase(seriesWithCalculatedPrices)
                cacheSeriesData(seriesWithCalculatedPrices, "服务器数据")
                return@flow
            }
        } catch (e: Exception) {
            android.util.Log.w("SeriesRepository", "服务器数据获取失败", e)
        }

        // 🔥 服务器也失败，使用预置数据兜底
        android.util.Log.w("SeriesRepository", "服务器数据获取失败，使用预置数据兜底")
        val presetData = getPresetData()
        val recalculatedPresetData = dataLayerManager.recalculateAllSeriesPrices(presetData)
        cacheSeriesData(recalculatedPresetData, "预置数据")
        emit(Resource.Success(recalculatedPresetData, "预置数据"))
    }

    /**
     * 🔥 新增：并行获取本地数据，提升性能
     */
    private suspend fun getCombinedLocalDataParallel(): List<Series> = withContext(Dispatchers.IO) {
        return@withContext try {
            android.util.Log.d("SeriesRepository", "🚀 开始并行获取本地数据")

            // 🔥 并行获取多个数据源
            val deferredSeries = async {
                android.util.Log.d("SeriesRepository", "并行获取系列数据")
                seriesDao.getSeriesWithCategories().first()
            }

            val deferredProgress = async {
                android.util.Log.d("SeriesRepository", "并行获取播放进度")
                playProgressDao.getAllProgress().first()
            }

            val deferredCaches = async {
                android.util.Log.d("SeriesRepository", "并行获取缓存信息")
                cacheInfoDao.getAllCacheInfo().first()
            }

            // 🔥 等待所有并行任务完成
            val seriesWithCategories = deferredSeries.await()
            val progressList = deferredProgress.await()
            val cacheList = deferredCaches.await()

            android.util.Log.d("SeriesRepository", "✅ 并行数据获取完成: ${seriesWithCategories.size}个系列")

            if (seriesWithCategories.isNotEmpty()) {
                // 转换为Domain模型
                val domainSeries = seriesWithCategories.map { seriesWithCategory ->
                    seriesWithCategory.toDomain(progressList, cacheList)
                }

                // 🔥 并行处理价格计算
                val processedSeries = async(Dispatchers.Default) {
                    android.util.Log.d("SeriesRepository", "并行处理价格计算")
                    dataLayerManager.recalculateAllSeriesPrices(domainSeries)
                }.await()

                android.util.Log.d("SeriesRepository", "✅ 并行数据处理完成")
                processedSeries
            } else {
                android.util.Log.w("SeriesRepository", "本地数据为空")
                emptyList()
            }

        } catch (e: Exception) {
            android.util.Log.e("SeriesRepository", "并行获取本地数据失败", e)
            // 降级到原有方法
            getCombinedLocalData()
        }
    }

    /**
     * 🔥 新增：后台静默更新服务器数据（增量更新版本）
     */
    private suspend fun updateServerDataInBackground() {
        try {
            android.util.Log.d("SeriesRepository", "🔄 后台静默增量更新服务器数据")
            val remoteSeries = seriesApi.getAllSeries().body()
            if (remoteSeries != null) {
                android.util.Log.d("SeriesRepository", "✅ 后台更新成功: ${remoteSeries.size}个系列")
                val domainSeries = remoteSeries.map { it.toDomainModel() }
                val serverData = dataLayerManager.recalculateAllSeriesPrices(domainSeries)

                // 🔥 获取本地数据进行差异比较
                val localData = getCombinedLocalDataParallel()

                // 🔥 检测数据差异
                val diffResult = dataDiffDetector.detectChanges(serverData, localData)

                if (diffResult.hasChanges) {
                    android.util.Log.d("SeriesRepository", "🔍 检测到数据变化，执行增量更新")
                    android.util.Log.d("SeriesRepository", "变化统计: ${diffResult.getTotalChanges()}项")

                    // 🔥 执行增量更新（购买状态以服务器为准）
                    incrementalUpdateStrategy.performIncrementalUpdate(diffResult, serverData)

                    // 🔥 通知UI数据已更新
                    dataChangeNotifier.notifyFromDiffResult(diffResult)

                    // 更新内存缓存
                    cacheSeriesData(serverData, "服务器数据(增量)")

                    android.util.Log.d("SeriesRepository", "✅ 增量更新完成，UI已通知")
                } else {
                    android.util.Log.d("SeriesRepository", "📊 数据无变化，跳过更新")
                }
            } else {
                android.util.Log.w("SeriesRepository", "后台更新：服务器返回空数据")
            }
        } catch (e: Exception) {
            android.util.Log.w("SeriesRepository", "后台增量更新失败（静默处理）", e)
            // 静默处理失败，不影响用户体验
        }
    }

    /**
     * 🔥 新增：暴露数据变化监听接口
     * 供ViewModel监听数据变化
     */
    override fun observeDataChanges() = dataChangeNotifier.dataChanges

    /**
     * 🔥 新增：保存数据到本地数据库
     */
    private suspend fun saveToLocalDatabase(seriesList: List<Series>) {
        try {
            // 🔥 使用事务确保数据一致性和外键约束
            val allSeriesEntities = seriesList.map { it.toEntity() }
            val allCategoryEntities = seriesList.flatMap { series ->
                series.categories.map { it.toEntity() }
            }
            val allVideoEntities = seriesList.flatMap { series ->
                series.categories.flatMap { category ->
                    category.videos.map { it.toEntity() }
                }
            }

            // 🔥 新增：使用事务确保数据一致性
            database.withTransaction {
                // 1. 清空所有表（保持外键约束顺序）
                videoDao.clearAll()
                categoryDao.clearAll()
                seriesDao.clearAll()

                // 2. 插入新数据（保持外键约束顺序）
                seriesDao.insertSeries(allSeriesEntities)
                categoryDao.insertCategories(allCategoryEntities)
                videoDao.insertVideos(allVideoEntities)

                android.util.Log.d("SeriesRepository", "✅ 数据库保存完成")
            }
        } catch (e: Exception) {
            android.util.Log.e("SeriesRepository", "保存到本地数据库失败", e)
        }
    }

    private suspend fun getCombinedLocalData(): List<Series> {
        val seriesWithCategories = seriesDao.getSeriesWithCategories().first()
        val progresses = playProgressDao.getAllProgress().first()
        val caches = cacheInfoDao.getAllCacheInfo().first()

        android.util.Log.d("SeriesRepository", "本地数据合并: ${caches.size}个缓存项")

        return seriesWithCategories.map { seriesWithCategory ->
            val series = seriesWithCategory.toDomain(progresses, caches)
            val enhancedCategories = series.categories.map { category ->
                val enhancedVideos = category.videos.map { video ->
                    val localState = try {
                        dataLayerManager.getLocalVideoState(video.id)
                    } catch (e: Exception) {
                        null
                    }
                    dataLayerManager.mergeVideoData(video, localState)
                }
                category.copy(videos = enhancedVideos)
            }
            series.copy(categories = enhancedCategories)
        }
    }

    /**
     * 检测数据是否有显著变化
     * 只有在内容、购买状态、价格等关键信息变化时才返回true
     */
    private fun detectDataChanges(localData: List<Series>, serverData: List<Series>): Boolean {
        try {
            // 1. 数量变化
            if (localData.size != serverData.size) {
                android.util.Log.d("SeriesRepository", "系列数量变化: ${localData.size} -> ${serverData.size}")
                return true
            }

            // 2. 内容变化检测
            localData.forEachIndexed { index, localSeries ->
                val serverSeries = serverData.getOrNull(index) ?: return true

                // 检查关键属性变化
                if (localSeries.id != serverSeries.id ||
                    localSeries.title != serverSeries.title ||
                    localSeries.price != serverSeries.price ||
                    localSeries.isPurchased != serverSeries.isPurchased ||
                    localSeries.categories.size != serverSeries.categories.size) {
                    return true
                }
            }

            return false
        } catch (e: Exception) {
            android.util.Log.e("SeriesRepository", "数据变化检测失败", e)
            return true // 检测失败时保守地认为有变化
        }
    }

    /**
     * 缓存系列数据到内存，实现丝滑的启动页到首页过渡
     */
    private fun cacheSeriesData(data: List<Series>, dataSource: String = "未知") {
        cachedSeriesData = data
        cacheTimestamp = System.currentTimeMillis()
        cacheDataSource = dataSource
        android.util.Log.d("SeriesRepository", "✅ 数据已缓存到内存，${data.size}个系列，数据源: $dataSource")
    }

    /**
     * 检查内存缓存是否有效
     */
    private fun isCacheValid(): Boolean {
        val isValid = cachedSeriesData != null &&
                     (System.currentTimeMillis() - cacheTimestamp) < seriesDataCacheValidDuration

        if (isValid) {
            val cacheAge = (System.currentTimeMillis() - cacheTimestamp) / 1000 / 60 // 分钟
            android.util.Log.d("SeriesRepository", "✅ 内存缓存有效，${cachedSeriesData?.size}个系列，缓存年龄: ${cacheAge}分钟")
        } else {
            val cacheAge = if (cacheTimestamp > 0) (System.currentTimeMillis() - cacheTimestamp) / 1000 / 60 else 0
            android.util.Log.d("SeriesRepository", "❌ 内存缓存无效或过期，缓存年龄: ${cacheAge}分钟")
        }
        return isValid
    }

    /**
     * 🔥 新增：获取预置数据（用于调试模式）
     */
    private fun getPresetData(): List<Series> {
        return try {
            val presetJson = context.assets.open("preset_data.json").bufferedReader().use { it.readText() }
            val presetSeriesDto = gson.fromJson(presetJson, Array<SeriesDto>::class.java).toList()
            val presetData = presetSeriesDto.map { it.toDomainModel() }
            // 🔥 关键：使用批量计算函数，支持全套课程动态计算
            dataLayerManager.recalculateAllSeriesPrices(presetData)

            android.util.Log.d("SeriesRepository", "预置数据加载完成: ${presetData.size}个系列")
            presetData
        } catch (e: Exception) {
            android.util.Log.e("SeriesRepository", "预置数据加载失败", e)
            emptyList()
        }
    }

    /**
     * 🔥 新增：检查是否有预加载数据
     */
    override fun hasPreloadedData(): Boolean {
        val hasData = cachedSeriesData != null && isPreloadCompleted
        android.util.Log.d("SeriesRepository", "检查预加载数据: hasData=$hasData, cacheSize=${cachedSeriesData?.size}, isCompleted=$isPreloadCompleted")
        return hasData
    }

    /**
     * 🔥 新增：获取预加载数据
     */
    override fun getPreloadedData(): Pair<List<Series>, String>? {
        return if (hasPreloadedData() && cachedSeriesData != null) {
            android.util.Log.d("SeriesRepository", "返回预加载数据: ${cachedSeriesData?.size}个系列, 数据源: $preloadDataSource")
            Pair(cachedSeriesData!!, preloadDataSource)
        } else {
            android.util.Log.w("SeriesRepository", "没有预加载数据可用，cachedData=${cachedSeriesData?.size}, isCompleted=$isPreloadCompleted")
            null
        }
    }

    /**
     * 🔥 新增：标记预加载完成
     */
    override fun markPreloadCompleted(dataSource: String) {
        isPreloadCompleted = true
        preloadDataSource = dataSource
        android.util.Log.d("SeriesRepository", "标记预加载完成: 数据源=$dataSource")
    }
}
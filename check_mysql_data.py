#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MySQL数据库中的数据
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

from database.models import DatabaseManager
from utils.config import Config

def check_mysql_data():
    """检查MySQL数据库中的数据"""
    print("🔍 检查MySQL数据库中的数据...")
    
    try:
        config = Config()
        db_config = config.get_database_config()
        db_manager = DatabaseManager(db_config)
        session = db_manager.get_session()
        
        if not session:
            print("❌ 无法连接数据库")
            return
        
        # 检查各个表的数据量
        tables = ['series', 'categories', 'videos', 'users']
        
        for table in tables:
            try:
                result = session.execute(f"SELECT COUNT(*) FROM {table}")
                count = result.fetchone()[0]
                print(f"📊 {table} 表: {count} 条记录")
                
                if count > 0:
                    # 显示前几条数据
                    result = session.execute(f"SELECT * FROM {table} LIMIT 3")
                    rows = result.fetchall()
                    print(f"   前3条数据:")
                    for i, row in enumerate(rows, 1):
                        print(f"     {i}. {dict(row._mapping)}")
                    print()
                    
            except Exception as e:
                print(f"❌ 检查表 {table} 失败: {e}")
        
        session.close()
        
    except Exception as e:
        print(f"❌ 检查MySQL数据异常: {e}")

def clear_mysql_data():
    """清空MySQL数据库中的数据"""
    print("🗑️ 清空MySQL数据库中的数据...")
    
    try:
        config = Config()
        db_config = config.get_database_config()
        db_manager = DatabaseManager(db_config)
        session = db_manager.get_session()
        
        if not session:
            print("❌ 无法连接数据库")
            return
        
        # 清空各个表的数据（保留表结构）
        tables = ['videos', 'categories', 'series', 'users']  # 注意顺序，先删除有外键的表
        
        for table in tables:
            try:
                session.execute(f"DELETE FROM {table}")
                print(f"✅ 清空表 {table}")
            except Exception as e:
                print(f"❌ 清空表 {table} 失败: {e}")
        
        session.commit()
        session.close()
        print("🎉 MySQL数据库清空完成")
        
    except Exception as e:
        print(f"❌ 清空MySQL数据异常: {e}")

def main():
    """主函数"""
    print("🚀 开始检查和清理MySQL数据...")
    print("=" * 50)
    
    # 先检查数据
    check_mysql_data()
    
    print("-" * 30)
    
    # 询问是否清空
    response = input("是否要清空MySQL数据库中的所有数据？(y/N): ")
    if response.lower() == 'y':
        clear_mysql_data()
        print("\n再次检查数据:")
        check_mysql_data()
    else:
        print("跳过清空操作")

if __name__ == "__main__":
    main()
